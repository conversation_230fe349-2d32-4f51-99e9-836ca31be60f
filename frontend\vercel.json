{"buildCommand": "npm run build", "outputDirectory": "dist", "installCommand": "npm install", "devCommand": "npm run dev", "framework": "vite", "rewrites": [{"source": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.).*)", "destination": "/index.html"}], "headers": [{"source": "/service-worker.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}