{"name": "ledgrio-school-accounting-system", "version": "1.0.0", "description": "Ledgrio School Accounting System - comprehensive financial management solution for educational institutions", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:api\" \"npm run dev:frontend\"", "dev:api": "cd api && npm run dev", "dev:frontend": "cd frontend && npm run dev", "start": "concurrently \"npm run start:api\" \"npm run start:frontend\"", "start:api": "cd api && npm start", "start:frontend": "cd frontend && npm run build && npm run preview", "install:all": "npm install && cd api && npm install && cd ../frontend && npm install", "build": "cd frontend && npm run build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["school", "accounting", "financial-management", "education", "fee-collection", "payment-processing", "audit", "compliance", "educational-institutions"], "author": "Ledgrio Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/charlykso/smart-s.git"}, "bugs": {"url": "https://github.com/charlykso/smart-s/issues"}, "homepage": "https://github.com/charlykso/smart-s#readme", "dependencies": {"axios": "^1.10.0", "form-data": "^4.0.2", "jsdom": "^26.1.0", "xlsx": "^0.18.5"}}