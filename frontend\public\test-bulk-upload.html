<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Bulk Upload</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, button { padding: 8px; margin: 5px 0; }
        button { background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; border-radius: 4px; white-space: pre-wrap; font-family: monospace; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Bulk Upload</h1>
        <p>This page tests the bulk upload functionality directly from the frontend context.</p>
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>" />
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="password123" />
        </div>
        
        <div class="form-group">
            <button onclick="login()">Login</button>
        </div>
        
        <div class="form-group">
            <label for="schoolId">School ID:</label>
            <input type="text" id="schoolId" value="68710d460f09d66cf283b298" />
        </div>
        
        <div class="form-group">
            <label for="file">Excel File:</label>
            <input type="file" id="file" accept=".xlsx,.xls" />
        </div>
        
        <div class="form-group">
            <button onclick="testUpload()">Test Upload</button>
        </div>
        
        <div id="log" class="log"></div>
    </div>

    <script>
        let token = null;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'log error' : type === 'success' ? 'log success' : 'log';
            logDiv.className = className;
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            console.log(message);
        }
        
        async function login() {
            try {
                log('🔐 Attempting login...');
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password }),
                });
                
                if (!response.ok) {
                    throw new Error(`Login failed: ${response.status} ${response.statusText}`);
                }
                
                const result = await response.json();
                token = result.data.token;
                log('✅ Login successful!', 'success');
                log(`Token: ${token.substring(0, 20)}...`);
                
            } catch (error) {
                log(`❌ Login failed: ${error.message}`, 'error');
            }
        }
        
        async function testUpload() {
            try {
                if (!token) {
                    log('❌ Please login first!', 'error');
                    return;
                }
                
                const fileInput = document.getElementById('file');
                const schoolId = document.getElementById('schoolId').value;
                
                if (!fileInput.files[0]) {
                    log('❌ Please select a file!', 'error');
                    return;
                }
                
                log('🔧 Starting bulk upload test...');
                log(`File: ${fileInput.files[0].name}`);
                log(`School ID: ${schoolId}`);
                
                const formData = new FormData();
                formData.append('file', fileInput.files[0]);
                formData.append('school_id', schoolId);
                
                log('📤 Making request to /api/v1/bulk-students/upload...');
                
                const response = await fetch('/api/v1/bulk-students/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                    body: formData,
                });
                
                log(`📥 Response received:`);
                log(`Status: ${response.status} ${response.statusText}`);
                log(`Content-Type: ${response.headers.get('content-type')}`);
                log(`Content-Length: ${response.headers.get('content-length')}`);
                
                const contentType = response.headers.get('content-type');
                
                if (!response.ok) {
                    if (contentType?.includes('application/json')) {
                        const result = await response.json();
                        log(`❌ Upload failed:`, 'error');
                        log(`Message: ${result.message}`);
                        if (result.conflicts) {
                            log(`Conflicts: ${JSON.stringify(result.conflicts, null, 2)}`);
                        }
                        if (result.errors) {
                            log(`Errors: ${JSON.stringify(result.errors, null, 2)}`);
                        }
                    } else {
                        const text = await response.text();
                        log(`❌ Upload failed: ${text}`, 'error');
                    }
                    return;
                }
                
                if (contentType?.includes('application/pdf')) {
                    log('✅ PDF response received!', 'success');
                    const blob = await response.blob();
                    log(`PDF size: ${blob.size} bytes`);
                    
                    // Download the PDF
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'student_credentials.pdf';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                    
                    log('✅ PDF download initiated!', 'success');
                } else {
                    log('✅ Upload successful!', 'success');
                    const result = await response.json();
                    log(`Result: ${JSON.stringify(result, null, 2)}`);
                }
                
            } catch (error) {
                log(`❌ Upload error: ${error.message}`, 'error');
                log(`Error type: ${error.constructor.name}`);
                log(`Error stack: ${error.stack}`);
            }
        }
    </script>
</body>
</html>
