@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  html {
    font-family:
      'Open Sans',
      ui-sans-serif,
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      'Helvetica Neue',
      <PERSON><PERSON>,
      'Noto Sans',
      sans-serif,
      'Apple Color Emoji',
      'Segoe UI Emoji',
      'Segoe UI Symbol',
      'Noto Color Emoji';
  }

  body {
    @apply font-sans antialiased;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
  }
}

/* Custom component styles */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 focus:ring-primary-500 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-secondary-100 hover:bg-secondary-200 focus:ring-secondary-500 text-secondary-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .input-field {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }

  .text-primary {
    @apply text-primary-600;
  }

  .text-secondary {
    @apply text-secondary-600;
  }
}

/* Custom utilities */
@layer utilities {
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  /* Text utilities for better card layouts */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Card responsive utilities */
  .card-responsive {
    @apply w-full h-full flex flex-col;
  }

  /* Table responsive utilities */
  .table-container {
    @apply overflow-x-auto shadow ring-1 ring-black ring-opacity-5 md:rounded-lg;
  }

  .table-cell-truncate {
    @apply truncate max-w-0;
  }

  .table-cell-user {
    @apply min-w-0 px-3 py-4;
  }

  .table-cell-fixed {
    @apply whitespace-nowrap px-3 py-4;
  }

  /* Navigation responsive utilities */
  .nav-tabs-responsive {
    @apply flex flex-wrap gap-2 sm:gap-4 lg:gap-6 xl:gap-8;
  }

  .nav-tab-button {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 whitespace-nowrap;
  }

  .nav-tab-icon {
    @apply h-4 w-4 mr-2 flex-shrink-0;
  }

  .nav-tab-text-responsive {
    @apply hidden sm:inline;
  }

  .nav-tab-text-mobile {
    @apply sm:hidden;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* React Hot Toast Overrides for Success/Error Distinction */
/* Base toast styling */
.custom-toast {
  border-radius: 10px !important;
  font-weight: 700 !important;
  min-width: 300px !important;
  padding: 16px !important;
}

/* Success Toast Class Styling */
.custom-toast-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: #ffffff !important;
  border: 2px solid #10b981 !important;
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.4) !important;
  border-radius: 10px !important;
  font-weight: 700 !important;
  min-width: 300px !important;
  padding: 16px !important;
}

/* Error Toast Class Styling */
.custom-toast-error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  color: #ffffff !important;
  border: 2px solid #ef4444 !important;
  box-shadow: 0 8px 32px rgba(239, 68, 68, 0.4) !important;
  border-radius: 10px !important;
  font-weight: 700 !important;
  min-width: 300px !important;
  padding: 16px !important;
}

/* Additional Specificity - Target any toast with success colors */
div[data-hot-toast] .custom-toast-success,
div[data-hot-toast] div[class*='custom-toast-success'],
div[role='status'].custom-toast-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: #ffffff !important;
  border: 2px solid #10b981 !important;
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.4) !important;
}

/* Additional Specificity - Target any toast with error colors */
div[data-hot-toast] .custom-toast-error,
div[data-hot-toast] div[class*='custom-toast-error'],
div[role='status'].custom-toast-error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  color: #ffffff !important;
  border: 2px solid #ef4444 !important;
  box-shadow: 0 8px 32px rgba(239, 68, 68, 0.4) !important;
}

/* Fallback - Override inline styles for any toast with green in background */
div[data-hot-toast] div[style*='#10b981'],
div[data-hot-toast] div[style*='16, 185, 129'],
div[data-hot-toast] div[style*='rgb(16, 185, 129)'] {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: #ffffff !important;
  border: 2px solid #10b981 !important;
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.4) !important;
}

/* Fallback - Override inline styles for any toast with red in background */
div[data-hot-toast] div[style*='#ef4444'],
div[data-hot-toast] div[style*='239, 68, 68'],
div[data-hot-toast] div[style*='rgb(239, 68, 68)'] {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  color: #ffffff !important;
  border: 2px solid #ef4444 !important;
  box-shadow: 0 8px 32px rgba(239, 68, 68, 0.4) !important;
}
