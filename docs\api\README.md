# API Documentation

This directory contains all backend API-related documentation.

## Files

### 📚 API Reference

- **API_DOCUMENTATION.md** - Complete API reference, endpoints, request/response formats, authentication, and usage examples

### 🔍 Backend Features

- **AUDITING_BACKEND_ENHANCEMENTS.md** - Backend auditing system implementation, database changes, and audit trail functionality
- **STUDENT_PAYMENT_REPORTS.md** - Payment reports API endpoints, data structures, and integration guidelines

## Quick Links

- [Main Documentation](../README.md)
- [Frontend Documentation](../frontend/)
- [Testing Documentation](../testing/)

## API Overview

The Smart School Management System API provides:

- RESTful endpoints for all system operations
- JWT-based authentication
- Role-based access control
- Comprehensive audit logging
- Payment processing integration
- Bulk data operations

## Getting Started

1. Review [API_DOCUMENTATION.md](./API_DOCUMENTATION.md) for complete endpoint reference
2. Check [Testing Documentation](../testing/) for test credentials
3. See [Implementation Guides](../implementation/) for role-specific functionality
