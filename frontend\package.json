{"name": "ledgrio-frontend", "private": true, "version": "0.0.0", "type": "module", "description": "Frontend application for Ledgrio School Accounting System - comprehensive financial management for educational institutions", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "prepare": "husky install"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tanstack/react-query": "^5.79.0", "autoprefixer": "^10.4.20", "axios": "^1.9.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.15.0", "lucide-react": "^0.513.0", "postcss": "^8.4.47", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.1", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.11", "zod": "^3.25.46", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^22.15.30", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "terser": "^5.41.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}