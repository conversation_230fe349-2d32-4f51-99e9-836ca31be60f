{
    id: 4985129907,
    domain: 'test',
    status: 'success',
    reference: 'm9so15zadu',
    receipt_number: null,
    amount: 100000,
    message: null,
    gateway_response: 'Successful',
    paid_at: '2025-05-21T11: 01: 41.000Z',
    created_at: '2025-05-21T11: 00: 47.000Z',
    channel: 'card',
    currency: 'NGN',
    ip_address: '***************',
    metadata: { custom_fields: [
            [Object
            ]
        ]
    },
    log: {
      start_time: **********,
      time_spent: 8,
      attempts: 1,
      errors: 0,
      success: true,
      mobile: false,
      input: [],
      history: [
            [Object
            ],
            [Object
            ]
        ]
    },
    fees: 1500,
    fees_split: null,
    authorization: {
      authorization_code: 'AUTH_qtkm61uckn',
      bin: '408408',
      last4: '4081',
      exp_month: '12',
      exp_year: '2030',
      channel: 'card',
      card_type: 'visa ',
      bank: 'TEST BANK',
      country_code: 'NG',
      brand: 'visa',
      reusable: true,
      signature: 'SIG_PRCzzoXQVWr7Sownr3pu',
      account_name: null,
      receiver_bank_account_number: null,
      receiver_bank: null
    },
    customer: {
      id: *********,
      first_name: null,
      last_name: null,
      email: '<EMAIL>',
      customer_code: 'CUS_4o7pzqhgukb9gog',
      phone: null,
      metadata: null,
      risk_action: 'default',
      international_format_phone: null
    },
    plan: null,
    split: {},
    order_id: null,
    paidAt: '2025-05-21T11: 01: 41.000Z',
    createdAt: '2025-05-21T11: 00: 47.000Z',
    requested_amount: 100000,
    pos_transaction_data: null,
    source: null,
    fees_breakdown: null,
    connect: null,
    transaction_date: '2025-05-21T11: 00: 47.000Z',
    plan_object: {},
    subaccount: {}
}