# Student Payment Reports - Complete Integration Guide

## Overview

This guide provides step-by-step instructions for integrating comprehensive PDF payment reports into the Ledgrio school accounting system, with support for term, session, and custom date range filtering.

## Backend Integration Steps

### 1. Install Required Dependencies

```bash
cd api
npm install pdfkit moment nodemailer
```

### 2. Create Required Files

#### Create Reports Route File

```bash
# Create the reports route file
touch routes/reports.js
```

#### Create Utility Files

```bash
# Create utility directories and files
mkdir -p utils/pdf
touch utils/pdf/pdfGenerator.js
touch utils/pdf/pdfStyles.js
touch utils/paymentFilters.js
touch utils/paymentAggregation.js
touch utils/emailReports.js
```

### 3. Update Environment Variables

Add to your `.env` file:

```env
# Email Configuration (for report delivery)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# PDF Configuration
PDF_LOGO_PATH=./assets/logo.png
```

### 4. Update Main Server File

Add to `server.js`:

```javascript
// Add reports routes
const reportRoutes = require('./routes/reports')
app.use('/api/v1/reports', reportRoutes)
```

### 5. Create Database Indexes for Performance

Add to your database initialization:

```javascript
// Add indexes for better report performance
db.payments.createIndex({ user: 1, trans_date: -1 })
db.payments.createIndex({ fee: 1, status: 1 })
db.payments.createIndex({ trans_date: 1 })
```

## Frontend Integration Steps

### 1. Install Required Dependencies

```bash
cd frontend
npm install file-saver @types/file-saver
```

### 2. Update Package Requirements

Add to your `package.json`:

```json
{
  "dependencies": {
    "file-saver": "^2.0.5"
  },
  "devDependencies": {
    "@types/file-saver": "^2.0.5"
  }
}
```

### 3. Create Report Components Directory

```bash
mkdir -p src/components/reports
mkdir -p src/services/reports
mkdir -p src/hooks/reports
mkdir -p src/types/reports
```

## API Endpoints Summary

### Available Endpoints

#### 1. Single Student Report (PDF Download)

```
GET /api/v1/reports/student/:student_id/payments/pdf
```

**Query Parameters:**

- `term_id` (optional): Filter by specific term
- `session_id` (optional): Filter by specific session
- `date_from` (optional): Start date for custom range (YYYY-MM-DD)
- `date_to` (optional): End date for custom range (YYYY-MM-DD)
- `include_pending` (optional): Include pending payments (default: false)
- `report_type` (optional): 'detailed' or 'summary' (default: 'detailed')

**Example Usage:**

```javascript
// Get current term report
GET /api/v1/reports/student/64a1b2c3d4e5f6789/payments/pdf?term_id=64b2c3d4e5f6789a

// Get session report
GET /api/v1/reports/student/64a1b2c3d4e5f6789/payments/pdf?session_id=64c3d4e5f6789ab1

// Get custom date range report
GET /api/v1/reports/student/64a1b2c3d4e5f6789/payments/pdf?date_from=2024-01-01&date_to=2024-03-31

// Get all-time summary report
GET /api/v1/reports/student/64a1b2c3d4e5f6789/payments/pdf?report_type=summary
```

#### 2. Multiple Students Report (PDF Download)

```
POST /api/v1/reports/students/payments/pdf
```

**Request Body:**

```json
{
  "student_ids": ["64a1b2c3d4e5f6789", "64b2c3d4e5f6789a"],
  "term_id": "64c3d4e5f6789ab1",
  "include_pending": false,
  "report_type": "summary"
}
```

#### 3. Class Report (PDF Download)

```
POST /api/v1/reports/class/:class_id/payments/pdf
```

#### 4. Email Report Delivery

```
POST /api/v1/reports/student/:student_id/payments/email
```

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "term_id": "64c3d4e5f6789ab1",
  "report_type": "detailed"
}
```

## Frontend Usage Examples

### 1. Quick Report Buttons

```typescript
// In Student Dashboard/Profile
import { QuickReportButtons } from '@/components/reports/QuickReportButtons'
;<QuickReportButtons
  studentId='64a1b2c3d4e5f6789'
  currentTermId='64c3d4e5f6789ab1'
  currentSessionId='64d4e5f6789ab12c'
/>
```

### 2. Advanced Report Form

```typescript
// In Reports Page
import { PaymentReportForm } from '@/components/reports/PaymentReportForm'
;<PaymentReportForm
  students={students}
  sessions={sessions}
  terms={terms}
  defaultStudentId={selectedStudentId}
/>
```

### 3. Programmatic Report Generation

```typescript
// Using the hook directly
import { useStudentPaymentReport } from '@/hooks/usePaymentReports'

const { generateReport, isGenerating } = useStudentPaymentReport()

const handleGenerateReport = () => {
  generateReport({
    student_id: '64a1b2c3d4e5f6789',
    term_id: '64c3d4e5f6789ab1',
    report_type: 'detailed',
    include_pending: false,
  })
}
```

## Role-Based Access Control

### Permissions by Role

#### Students

- ✅ Can generate their own payment reports
- ❌ Cannot access other students' reports
- ✅ Can use all filtering options (term, session, date range)

#### Parents

- ✅ Can generate reports for their linked children
- ❌ Cannot access unrelated students' reports
- ✅ Can email reports to themselves

#### Teachers/Staff

- ✅ Can generate reports for students in their classes
- ✅ Can generate class-wide reports
- ✅ Can email reports to parents

#### Administrators/Principals/Bursars

- ✅ Full access to all student payment reports
- ✅ Can generate batch reports
- ✅ Can access advanced filtering options
- ✅ Can email reports to any recipient

### Implementation in Routes

```javascript
// Example role-based access
router.get(
  '/student/:student_id/payments/pdf',
  authenticateToken,
  async (req, res) => {
    const { student_id } = req.params

    // Students can only access their own reports
    if (req.user.roles.includes('Student') && req.user.Id !== student_id) {
      return res.status(403).json({ message: 'Access denied' })
    }

    // Parents can only access their children's reports
    if (req.user.roles.includes('Parent')) {
      const isParentOfStudent = await checkParentStudentRelationship(
        req.user.Id,
        student_id
      )
      if (!isParentOfStudent) {
        return res.status(403).json({ message: 'Access denied' })
      }
    }

    // Continue with report generation...
  }
)
```

## Integration with Existing Audit System

### Audit Logging for Report Generation

```javascript
// Add audit logging to report generation
const { auditTransaction } = require('../middleware/auditMiddleware')

router.get(
  '/student/:student_id/payments/pdf',
  authenticateToken,
  auditTransaction('report_generation', 'payment_report'),
  async (req, res) => {
    // Report generation logic...
  }
)
```

### Audit Trail Integration

The payment reports automatically integrate with the existing audit system by:

- Logging all report generation activities
- Tracking who accessed which student's reports
- Recording report parameters (date ranges, filters)
- Maintaining download history for compliance

## Performance Considerations

### Optimization Strategies

#### 1. Database Indexing

```javascript
// Recommended indexes for report performance
db.payments.createIndex({ user: 1, trans_date: -1 })
db.payments.createIndex({ fee: 1, status: 1 })
db.fees.createIndex({ term: 1, isActive: 1 })
```

#### 2. Caching Strategy

```javascript
// Cache frequently requested reports
const NodeCache = require('node-cache')
const reportCache = new NodeCache({ stdTTL: 300 }) // 5 minutes

// Check cache before generating report
const cacheKey = `report_${student_id}_${JSON.stringify(filters)}`
const cachedReport = reportCache.get(cacheKey)
if (cachedReport) {
  return res.send(cachedReport)
}
```

#### 3. Pagination for Large Datasets

```javascript
// For students with many payments, implement pagination
const MAX_PAYMENTS_PER_REPORT = 1000
if (payments.length > MAX_PAYMENTS_PER_REPORT) {
  // Split into multiple pages or provide summary view
}
```

## Error Handling and Validation

### Common Error Scenarios

1. **Student not found**: Return 404 with clear message
2. **No payments found**: Generate report with "No payments" message
3. **Invalid date ranges**: Validate date parameters
4. **PDF generation failure**: Provide fallback or retry mechanism
5. **Email delivery failure**: Log error and notify user

### Validation Examples

```javascript
// Validate date range
if (date_from && date_to && new Date(date_from) > new Date(date_to)) {
  return res.status(400).json({
    message: 'Start date cannot be after end date',
  })
}

// Validate student access
const student = await User.findById(student_id)
if (!student) {
  return res.status(404).json({
    message: 'Student not found',
  })
}
```

## Testing the Implementation

### Backend Testing

```bash
# Test single student report
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:3000/api/v1/reports/student/STUDENT_ID/payments/pdf?term_id=TERM_ID" \
  --output test-report.pdf

# Test with date range
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:3000/api/v1/reports/student/STUDENT_ID/payments/pdf?date_from=2024-01-01&date_to=2024-03-31" \
  --output test-report-range.pdf
```

### Frontend Testing

```typescript
// Test report generation in browser console
const reportService = new ReportService()
await reportService.generateStudentPaymentReportPDF({
  student_id: 'STUDENT_ID',
  term_id: 'TERM_ID',
  report_type: 'detailed',
})
```

This comprehensive integration guide ensures seamless implementation of the PDF payment report system into the existing Ledgrio application with proper security, performance optimization, and audit trail integration.
