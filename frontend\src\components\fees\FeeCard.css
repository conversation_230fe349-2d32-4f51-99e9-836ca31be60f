/* Fee Card specific styles for proper text truncation and line clamping */

.fee-card-title {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  -webkit-hyphens: auto;
  hyphens: auto;
  line-height: 1.3;
  min-height: 2.6em; /* Ensure consistent height */
}

.fee-card-description {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  -webkit-hyphens: auto;
  hyphens: auto;
  line-height: 1.4;
  min-height: 4.2em; /* Ensure consistent height */
}

.fee-card-school-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.fee-card-term-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.fee-card-installment-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

/* Fallback for browsers that don't support -webkit-line-clamp */
.fee-card-title-fallback {
  max-height: 2.6em; /* approximately 2 lines */
  overflow: hidden;
  line-height: 1.3;
}

.fee-card-description-fallback {
  max-height: 4.2em; /* approximately 3 lines */
  overflow: hidden;
  line-height: 1.4;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .fee-card-title {
    -webkit-line-clamp: 1;
    line-clamp: 1;
    line-height: 1.2;
  }

  .fee-card-title-fallback {
    max-height: 1.2em;
    line-height: 1.2;
  }

  .fee-card-description {
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }

  .fee-card-description-fallback {
    max-height: 2.8em;
  }
}
