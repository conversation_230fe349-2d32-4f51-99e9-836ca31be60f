{"name": "ledgrio-api", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js"}, "keywords": ["school", "accounting", "financial-management", "education", "fee-collection"], "author": "Ledgrio Team", "license": "ISC", "description": "Backend API for Ledgrio School Accounting System - comprehensive financial management for educational institutions", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "cloudinary": "^2.6.1", "crypto": "^1.0.1", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "express": "^4.19.2", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.0", "multer": "^2.0.1", "nodemailer": "^7.0.3", "nodemon": "^3.1.10", "pdfkit": "^0.17.1", "uuid": "^11.1.0", "xlsx": "^0.18.5"}}